Running package command
Extra PyPi indexes: [https://pypi.flet.dev]
Created temp directory: C:\Users\<USER>\AppData\Local\Temp\serious_python_temp7a9a7e72
Copying Python app from C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\src to a temp directory
Configured Windows/ platform with sitecustomize.py
Installing [flet-confetti @ file://C:\Users\<USER>\Desktop\test, flet, --no-cache-dir] with pip command to C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\site-packages
Copying Flutter packages to C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\flutter-packages-temp
Cleanup installed packages
Creating app archive at C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\flutter\app/app.zip from a temp directory
Writing app archive hash to C:\Users\<USER>\Desktop\test\examples\flet_confetti_example\build\flutter\app/app.zip.hash
Deleting temp directory
