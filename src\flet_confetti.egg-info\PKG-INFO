Metadata-Version: 2.4
Name: flet-confetti
Version: 0.1.0
Summary: FletConfetti control for Flet
Author-email: Flet contributors <<EMAIL>>
Project-URL: Homepage, https://mydomain.dev
Project-URL: Documentation, https://github.com/MyGithubAccount/flet-confetti
Project-URL: Repository, https://github.com/MyGithubAccount/flet-confetti
Project-URL: Issues, https://github.com/MyGithubAccount/flet-confetti/issues
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: flet>=0.28.3

# flet-confetti
FletConfetti control for Flet

## Installation

Add dependency to `pyproject.toml` of your Flet app:

* **Git dependency**

Link to git repository:

```
dependencies = [
  "flet-confetti @ git+https://github.com/MyGithubAccount/flet-confetti",
  "flet>=0.28.3",
]
```

* **PyPi dependency**  

If the package is published on pypi.org:

```
dependencies = [
  "flet-confetti",
  "flet>=0.28.3",
]
```

Build your app:
```
flet build macos -v
```

## Documentation

[Link to documentation](https://MyGithubAccount.github.io/flet-confetti/)
