"""
FletConfetti Documentation Demo

This script demonstrates the comprehensive documentation and features
of the FletConfetti package. Run this to see all the docstrings in action
and test the complete API.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import flet as ft
from flet_confetti import (
    FletConfetti, 
    ConfettiTheme, 
    BlastDirectionality, 
    ParticleShape,
    THEME_COLORS
)
import math


def main(page: ft.Page):
    page.title = "FletConfetti - Complete Documentation Demo"
    page.bgcolor = ft.Colors.BLACK
    page.padding = 20
    page.scroll = ft.ScrollMode.AUTO
    
    # Demo confetti instances showcasing different features
    confetti_demo = FletConfetti(
        theme=ConfettiTheme.NEON,
        particle_shape=ParticleShape.STAR,
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        number_of_particles=20,
        max_blast_force=40,
        gravity=0.3,
        duration_seconds=4,
    )
    
    confetti_directional = FletConfetti(
        colors=["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4"],
        particle_shape=ParticleShape.HEART,
        blast_directionality=BlastDirectionality.DIRECTIONAL,
        blast_direction=math.pi / 2,  # Downward
        number_of_particles=15,
        emission_frequency=0.03,
        gravity=0.4,
        duration_seconds=3,
    )
    
    status_text = ft.Text(
        "🎉 FletConfetti Documentation Demo - Ready to celebrate!",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )
    
    def show_help(_):
        """Display comprehensive help information."""
        help_text = f"""
🎉 FLET CONFETTI - COMPLETE API DOCUMENTATION

📚 MAIN CLASS:
{FletConfetti.__doc__}

🎨 THEMES AVAILABLE:
{', '.join([theme.value for theme in ConfettiTheme])}

🔥 BLAST PATTERNS:
- DIRECTIONAL: {BlastDirectionality.DIRECTIONAL.__doc__}
- EXPLOSIVE: {BlastDirectionality.EXPLOSIVE.__doc__}

⭐ PARTICLE SHAPES:
{', '.join([shape.value for shape in ParticleShape])}

🎯 KEY PROPERTIES:
- emission_frequency: {confetti_demo.emission_frequency.__doc__}
- number_of_particles: {confetti_demo.number_of_particles.__doc__}
- gravity: {confetti_demo.gravity.__doc__}

🎮 CONTROL METHODS:
- play(): {confetti_demo.play.__doc__}
- stop(): {confetti_demo.stop.__doc__}
        """
        
        print("=" * 80)
        print("FLET CONFETTI - COMPLETE DOCUMENTATION")
        print("=" * 80)
        print(help_text)
        print("=" * 80)
        
        status_text.value = "📚 Documentation printed to console!"
        page.update()
    
    def demo_explosive(_):
        """Demonstrate explosive blast pattern."""
        print(f"🎆 EXPLOSIVE DEMO:")
        print(f"Theme: {confetti_demo.theme}")
        print(f"Shape: {confetti_demo.particle_shape}")
        print(f"Pattern: {confetti_demo.blast_directionality}")
        
        confetti_demo.play()
        status_text.value = "🎆 Explosive confetti burst!"
        page.update()
    
    def demo_directional(_):
        """Demonstrate directional blast pattern."""
        print(f"🎯 DIRECTIONAL DEMO:")
        print(f"Colors: {confetti_directional.colors}")
        print(f"Shape: {confetti_directional.particle_shape}")
        print(f"Direction: {confetti_directional.blast_direction} radians")
        
        confetti_directional.play()
        status_text.value = "🎯 Directional confetti stream!"
        page.update()
    
    def demo_themes(_):
        """Cycle through different themes."""
        themes = [ConfettiTheme.RAINBOW, ConfettiTheme.NEON, ConfettiTheme.PASTEL]
        current_theme = themes[0]
        
        confetti_demo.theme = current_theme
        confetti_demo.play()
        
        print(f"🌈 THEME DEMO: {current_theme.value}")
        print(f"Colors: {THEME_COLORS[current_theme.value]}")
        
        status_text.value = f"🌈 Theme: {current_theme.value.upper()}"
        page.update()
    
    def demo_shapes(_):
        """Cycle through different particle shapes."""
        shapes = [ParticleShape.SNOWFLAKE, ParticleShape.BUTTERFLY, ParticleShape.MUSIC_NOTE]
        current_shape = shapes[0]
        
        confetti_demo.particle_shape = current_shape
        confetti_demo.play()
        
        print(f"⭐ SHAPE DEMO: {current_shape.value}")
        
        status_text.value = f"⭐ Shape: {current_shape.value.upper()}"
        page.update()
    
    def stop_all(_):
        """Stop all confetti animations."""
        confetti_demo.stop(clear_all_particles=True)
        confetti_directional.stop(clear_all_particles=True)
        status_text.value = "🛑 All confetti stopped!"
        page.update()
    
    # Create the UI
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        ft.Text(
                            "🎉 FletConfetti Documentation Demo",
                            size=28,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER,
                        ),
                        
                        ft.Text(
                            "Complete API demonstration with professional docstrings",
                            size=16,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                        
                        status_text,
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=40),
                        
                        # Documentation button
                        ft.ElevatedButton(
                            "📚 Show Complete Documentation",
                            on_click=show_help,
                            bgcolor=ft.Colors.INDIGO_600,
                            color=ft.Colors.WHITE,
                            style=ft.ButtonStyle(
                                padding=ft.Padding(25, 15, 25, 15),
                            ),
                        ),
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=20),
                        
                        # Demo buttons
                        ft.Row([
                            ft.ElevatedButton(
                                "🎆 Explosive Demo",
                                on_click=demo_explosive,
                                bgcolor=ft.Colors.PURPLE_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🎯 Directional Demo",
                                on_click=demo_directional,
                                bgcolor=ft.Colors.BLUE_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        
                        ft.Row([
                            ft.ElevatedButton(
                                "🌈 Theme Demo",
                                on_click=demo_themes,
                                bgcolor=ft.Colors.PINK_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "⭐ Shape Demo",
                                on_click=demo_shapes,
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        
                        ft.ElevatedButton(
                            "🛑 Stop All",
                            on_click=stop_all,
                            bgcolor=ft.Colors.RED_600,
                            color=ft.Colors.WHITE,
                            style=ft.ButtonStyle(
                                padding=ft.Padding(20, 15, 20, 15),
                            ),
                        ),
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=20),
                        
                        ft.Text(
                            "✨ Professional Documentation Ready for Publication",
                            size=14,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            weight=ft.FontWeight.BOLD,
                        ),
                        
                        ft.Text(
                            "Complete docstrings • Type hints • Examples • Best practices",
                            size=12,
                            color=ft.Colors.WHITE60,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=15,
                ),
                
                # Confetti widgets
                confetti_demo,
                confetti_directional,
            ],
            alignment=ft.alignment.center,
        )
    )


if __name__ == "__main__":
    print("🎉 Starting FletConfetti Documentation Demo...")
    print("📚 This demo showcases the complete, professional documentation")
    print("🚀 Ready for public release!")
    print("-" * 60)
    ft.app(main)
