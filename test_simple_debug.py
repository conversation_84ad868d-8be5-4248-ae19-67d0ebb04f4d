import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import flet as ft
from flet_confetti import FletConfetti, ConfettiTheme

def main(page: ft.Page):
    page.title = "Simple Debug"
    page.bgcolor = ft.Colors.BLACK
    
    # Test NEON theme
    confetti = FletConfetti(theme=ConfettiTheme.NEON)
    
    # Debug what's being sent
    print(f"confetti.theme = {confetti.theme}")
    print(f"confetti._get_attr('theme') = {confetti._get_attr('theme')}")
    print(f"confetti._get_attr('colors') = {confetti._get_attr('colors')}")
    
    def test_play(_):
        print("Playing confetti...")
        confetti.play()
    
    page.add(
        ft.Column([
            ft.ElevatedButton("Play NEON", on_click=test_play),
            confetti
        ])
    )

if __name__ == "__main__":
    ft.app(main)
