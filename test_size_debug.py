"""
FletConfetti Size Debug Test

This script debugs the Size values being sent from Python to Dart
to identify where the size values are getting lost.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import flet as ft
from flet.core.size import Size
from flet_confetti import FletConfetti, ConfettiTheme, ParticleShape
import json


def main(page: ft.Page):
    page.title = "FletConfetti - Size Debug Test"
    page.bgcolor = ft.Colors.BLACK
    page.padding = 20
    
    # Create confetti with specific sizes for debugging
    confetti = FletConfetti(
        theme=ConfettiTheme.NEON,
        particle_shape=ParticleShape.CIRCLE,
        number_of_particles=20,
        duration_seconds=5,
    )
    
    status_text = ft.Text(
        "🔍 Size Debug Test - Check console for detailed output",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )
    
    def debug_default_sizes(_):
        """Debug the default sizes."""
        print("🔍 DEBUGGING DEFAULT SIZES")
        print("=" * 50)
        
        print(f"Default minimum_size: {confetti.minimum_size}")
        print(f"Default maximum_size: {confetti.maximum_size}")
        print(f"Default canvas_size: {confetti.canvas_size}")
        
        # Check what's actually being sent to Dart
        print(f"minimum_size attr: {confetti._get_attr('minimum_size')}")
        print(f"maximum_size attr: {confetti._get_attr('maximum_size')}")
        print(f"canvas_size attr: {confetti._get_attr('canvas_size')}")
        
        confetti.play()
        status_text.value = "🔍 Default sizes debugged - check console!"
        page.update()
    
    def debug_custom_sizes(_):
        """Debug custom sizes."""
        print("🎯 DEBUGGING CUSTOM SIZES")
        print("=" * 50)
        
        # Set custom sizes
        confetti.minimum_size = Size(10, 5)
        confetti.maximum_size = Size(50, 30)
        confetti.canvas_size = Size(800, 600)
        
        print(f"Set minimum_size: {confetti.minimum_size}")
        print(f"Set maximum_size: {confetti.maximum_size}")
        print(f"Set canvas_size: {confetti.canvas_size}")
        
        # Check what's actually being sent to Dart
        min_attr = confetti._get_attr('minimum_size')
        max_attr = confetti._get_attr('maximum_size')
        canvas_attr = confetti._get_attr('canvas_size')
        
        print(f"minimum_size attr: {min_attr}")
        print(f"maximum_size attr: {max_attr}")
        print(f"canvas_size attr: {canvas_attr}")
        
        # Try to parse the JSON to verify it's correct
        if min_attr:
            try:
                min_parsed = json.loads(min_attr) if isinstance(min_attr, str) else min_attr
                print(f"minimum_size parsed: {min_parsed}")
            except Exception as e:
                print(f"Error parsing minimum_size: {e}")
        
        if max_attr:
            try:
                max_parsed = json.loads(max_attr) if isinstance(max_attr, str) else max_attr
                print(f"maximum_size parsed: {max_parsed}")
            except Exception as e:
                print(f"Error parsing maximum_size: {e}")
        
        confetti.play()
        status_text.value = "🎯 Custom sizes debugged - check console!"
        page.update()
    
    def debug_extreme_sizes(_):
        """Debug with extreme sizes to make the difference obvious."""
        print("🚀 DEBUGGING EXTREME SIZES")
        print("=" * 50)
        
        # Set extreme sizes that should be very obvious
        confetti.minimum_size = Size(5, 2)    # Very small
        confetti.maximum_size = Size(100, 60) # Very large
        
        print(f"Extreme minimum_size: {confetti.minimum_size}")
        print(f"Extreme maximum_size: {confetti.maximum_size}")
        
        # Check attributes
        print(f"minimum_size attr: {confetti._get_attr('minimum_size')}")
        print(f"maximum_size attr: {confetti._get_attr('maximum_size')}")
        
        confetti.play()
        status_text.value = "🚀 Extreme sizes set - particles should be very varied!"
        page.update()
    
    def debug_size_objects(_):
        """Debug Size object creation and properties."""
        print("📏 DEBUGGING SIZE OBJECTS")
        print("=" * 50)
        
        # Test Size object creation
        test_size = Size(25, 15)
        print(f"Test Size object: {test_size}")
        print(f"Test Size width: {test_size.width}")
        print(f"Test Size height: {test_size.height}")
        
        # Test JSON serialization manually
        size_dict = {"width": test_size.width, "height": test_size.height}
        size_json = json.dumps(size_dict)
        print(f"Manual JSON serialization: {size_json}")
        
        # Test setting and getting
        confetti.minimum_size = test_size
        retrieved = confetti.minimum_size
        print(f"Set and retrieved Size: {retrieved}")
        
        status_text.value = "📏 Size objects debugged - check console!"
        page.update()
    
    def test_null_sizes(_):
        """Test with null/None sizes."""
        print("❌ DEBUGGING NULL SIZES")
        print("=" * 50)
        
        # Set sizes to None
        confetti.minimum_size = None
        confetti.maximum_size = None
        confetti.canvas_size = None
        
        print(f"Null minimum_size: {confetti.minimum_size}")
        print(f"Null maximum_size: {confetti.maximum_size}")
        print(f"Null canvas_size: {confetti.canvas_size}")
        
        # Check attributes
        print(f"minimum_size attr: {confetti._get_attr('minimum_size')}")
        print(f"maximum_size attr: {confetti._get_attr('maximum_size')}")
        print(f"canvas_size attr: {confetti._get_attr('canvas_size')}")
        
        confetti.play()
        status_text.value = "❌ Null sizes tested - should use defaults!"
        page.update()
    
    def stop_confetti(_):
        """Stop confetti."""
        confetti.stop(clear_all_particles=True)
        status_text.value = "🛑 Confetti stopped!"
        page.update()
    
    # Create the UI
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        ft.Text(
                            "🔍 FletConfetti Size Debug",
                            size=28,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER,
                        ),
                        
                        ft.Text(
                            "Debugging Size values from Python to Dart",
                            size=16,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                        
                        status_text,
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=40),
                        
                        # Debug buttons
                        ft.Row([
                            ft.ElevatedButton(
                                "🔍 Default Sizes",
                                on_click=debug_default_sizes,
                                bgcolor=ft.Colors.BLUE_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🎯 Custom Sizes",
                                on_click=debug_custom_sizes,
                                bgcolor=ft.Colors.PURPLE_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        
                        ft.Row([
                            ft.ElevatedButton(
                                "🚀 Extreme Sizes",
                                on_click=debug_extreme_sizes,
                                bgcolor=ft.Colors.ORANGE_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "📏 Size Objects",
                                on_click=debug_size_objects,
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        
                        ft.Row([
                            ft.ElevatedButton(
                                "❌ Null Sizes",
                                on_click=test_null_sizes,
                                bgcolor=ft.Colors.INDIGO_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🛑 Stop",
                                on_click=stop_confetti,
                                bgcolor=ft.Colors.RED_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=20),
                        
                        ft.Text(
                            "🔍 Debug output will appear in console",
                            size=14,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                        
                        ft.Text(
                            "Check both Python console and Flutter debug output",
                            size=12,
                            color=ft.Colors.WHITE60,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=15,
                ),
                
                # Confetti widget
                confetti,
            ],
            alignment=ft.alignment.center,
        )
    )


if __name__ == "__main__":
    print("🔍 Starting FletConfetti Size Debug Test...")
    print("This will help identify where size values are getting lost")
    print("-" * 60)
    ft.app(main)
