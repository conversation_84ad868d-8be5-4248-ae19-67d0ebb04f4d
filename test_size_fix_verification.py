"""
FletConfetti Size Fix Verification

This script tests the simplified Size parsing approach to verify that
particle sizes are now correctly changing when minimum_size and maximum_size
properties are modified.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import flet as ft
from flet.core.size import Size
from flet_confetti import FletConfetti, ConfettiTheme, ParticleShape, BlastDirectionality
import json


def main(page: ft.Page):
    page.title = "FletConfetti - Size Fix Verification"
    page.bgcolor = ft.Colors.BLACK
    page.padding = 20
    
    # Create confetti with default sizes
    confetti = FletConfetti(
        theme=ConfettiTheme.NEON,
        particle_shape=ParticleShape.CIRCLE,
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        number_of_particles=25,
        duration_seconds=6,
    )
    
    status_text = ft.Text(
        "🔧 Size Fix Verification - Testing simplified Size parsing",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )
    
    def test_default_sizes(_):
        """Test default sizes."""
        print("🔧 TESTING DEFAULT SIZES")
        print("=" * 50)
        
        # Reset to defaults
        confetti.minimum_size = Size(20, 10)
        confetti.maximum_size = Size(30, 15)
        
        print(f"Default minimum_size: {confetti.minimum_size}")
        print(f"Default maximum_size: {confetti.maximum_size}")
        
        # Check JSON serialization
        min_json = confetti._get_attr('minimum_size')
        max_json = confetti._get_attr('maximum_size')
        
        print(f"minimum_size JSON: {min_json}")
        print(f"maximum_size JSON: {max_json}")
        
        # Verify JSON structure
        if min_json:
            try:
                parsed = json.loads(min_json) if isinstance(min_json, str) else min_json
                print(f"minimum_size parsed: width={parsed.get('width')}, height={parsed.get('height')}")
            except Exception as e:
                print(f"Error parsing minimum_size JSON: {e}")
        
        confetti.play()
        status_text.value = "🔧 Default sizes (20x10 to 30x15) - Normal particles"
        page.update()
    
    def test_small_sizes(_):
        """Test very small particle sizes."""
        print("🔬 TESTING SMALL SIZES")
        print("=" * 50)
        
        # Set very small sizes
        confetti.minimum_size = Size(3, 2)
        confetti.maximum_size = Size(8, 5)
        
        print(f"Small minimum_size: {confetti.minimum_size}")
        print(f"Small maximum_size: {confetti.maximum_size}")
        
        # Check JSON
        min_json = confetti._get_attr('minimum_size')
        max_json = confetti._get_attr('maximum_size')
        print(f"Small minimum_size JSON: {min_json}")
        print(f"Small maximum_size JSON: {max_json}")
        
        confetti.play()
        status_text.value = "🔬 Small sizes (3x2 to 8x5) - Tiny particles!"
        page.update()
    
    def test_large_sizes(_):
        """Test very large particle sizes."""
        print("🚀 TESTING LARGE SIZES")
        print("=" * 50)
        
        # Set very large sizes
        confetti.minimum_size = Size(40, 25)
        confetti.maximum_size = Size(80, 50)
        
        print(f"Large minimum_size: {confetti.minimum_size}")
        print(f"Large maximum_size: {confetti.maximum_size}")
        
        # Check JSON
        min_json = confetti._get_attr('minimum_size')
        max_json = confetti._get_attr('maximum_size')
        print(f"Large minimum_size JSON: {min_json}")
        print(f"Large maximum_size JSON: {max_json}")
        
        confetti.play()
        status_text.value = "🚀 Large sizes (40x25 to 80x50) - Big particles!"
        page.update()
    
    def test_extreme_variation(_):
        """Test extreme size variation."""
        print("⚡ TESTING EXTREME VARIATION")
        print("=" * 50)
        
        # Set extreme size variation
        confetti.minimum_size = Size(2, 1)
        confetti.maximum_size = Size(100, 60)
        
        print(f"Extreme minimum_size: {confetti.minimum_size}")
        print(f"Extreme maximum_size: {confetti.maximum_size}")
        
        # Check JSON
        min_json = confetti._get_attr('minimum_size')
        max_json = confetti._get_attr('maximum_size')
        print(f"Extreme minimum_size JSON: {min_json}")
        print(f"Extreme maximum_size JSON: {max_json}")
        
        confetti.play()
        status_text.value = "⚡ Extreme variation (2x1 to 100x60) - Wild sizes!"
        page.update()
    
    def test_square_particles(_):
        """Test square particles."""
        print("⬜ TESTING SQUARE PARTICLES")
        print("=" * 50)
        
        # Set square sizes
        confetti.minimum_size = Size(15, 15)
        confetti.maximum_size = Size(40, 40)
        
        print(f"Square minimum_size: {confetti.minimum_size}")
        print(f"Square maximum_size: {confetti.maximum_size}")
        
        confetti.play()
        status_text.value = "⬜ Square particles (15x15 to 40x40) - Perfect squares!"
        page.update()
    
    def test_tall_particles(_):
        """Test tall, thin particles."""
        print("📏 TESTING TALL PARTICLES")
        print("=" * 50)
        
        # Set tall, thin sizes
        confetti.minimum_size = Size(5, 20)
        confetti.maximum_size = Size(10, 60)
        
        print(f"Tall minimum_size: {confetti.minimum_size}")
        print(f"Tall maximum_size: {confetti.maximum_size}")
        
        confetti.play()
        status_text.value = "📏 Tall particles (5x20 to 10x60) - Thin and tall!"
        page.update()
    
    def test_json_structure(_):
        """Test and display JSON structure."""
        print("📋 TESTING JSON STRUCTURE")
        print("=" * 50)
        
        # Set test sizes
        confetti.minimum_size = Size(25, 15)
        confetti.maximum_size = Size(45, 30)
        
        # Get and analyze JSON
        min_json = confetti._get_attr('minimum_size')
        max_json = confetti._get_attr('maximum_size')
        
        print(f"JSON Structure Analysis:")
        print(f"minimum_size raw: {repr(min_json)}")
        print(f"maximum_size raw: {repr(max_json)}")
        
        # Test parsing like Dart would
        if min_json:
            try:
                if isinstance(min_json, str):
                    parsed = json.loads(min_json)
                else:
                    parsed = min_json
                
                width = parsed.get('width')
                height = parsed.get('height')
                print(f"Dart would parse: width={width}, height={height}")
                print(f"Types: width={type(width)}, height={type(height)}")
                
                # Test conversion to double
                width_double = float(width) if width is not None else 20.0
                height_double = float(height) if height is not None else 10.0
                print(f"As doubles: width={width_double}, height={height_double}")
                
            except Exception as e:
                print(f"Error in JSON analysis: {e}")
        
        status_text.value = "📋 JSON structure analyzed - check console!"
        page.update()
    
    def stop_confetti(_):
        """Stop confetti."""
        confetti.stop(clear_all_particles=True)
        status_text.value = "🛑 Confetti stopped!"
        page.update()
    
    # Create the UI
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        ft.Text(
                            "🔧 Size Fix Verification",
                            size=28,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER,
                        ),
                        
                        ft.Text(
                            "Testing simplified Size parsing - particles should change size!",
                            size=16,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                        
                        status_text,
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=40),
                        
                        # Test buttons
                        ft.Row([
                            ft.ElevatedButton(
                                "🔧 Default",
                                on_click=test_default_sizes,
                                bgcolor=ft.Colors.BLUE_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🔬 Small",
                                on_click=test_small_sizes,
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🚀 Large",
                                on_click=test_large_sizes,
                                bgcolor=ft.Colors.PURPLE_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                        
                        ft.Row([
                            ft.ElevatedButton(
                                "⚡ Extreme",
                                on_click=test_extreme_variation,
                                bgcolor=ft.Colors.ORANGE_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "⬜ Square",
                                on_click=test_square_particles,
                                bgcolor=ft.Colors.INDIGO_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "📏 Tall",
                                on_click=test_tall_particles,
                                bgcolor=ft.Colors.PINK_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                        
                        ft.Row([
                            ft.ElevatedButton(
                                "📋 JSON Test",
                                on_click=test_json_structure,
                                bgcolor=ft.Colors.TEAL_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🛑 Stop",
                                on_click=stop_confetti,
                                bgcolor=ft.Colors.RED_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=20),
                        
                        ft.Text(
                            "✅ Fixed: Simplified Size parsing eliminates compatibility issues",
                            size=14,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            weight=ft.FontWeight.BOLD,
                        ),
                        
                        ft.Text(
                            "Direct JSON parsing • No complex functions • Better compatibility",
                            size=12,
                            color=ft.Colors.WHITE60,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=15,
                ),
                
                # Confetti widget
                confetti,
            ],
            alignment=ft.alignment.center,
        )
    )


if __name__ == "__main__":
    print("🔧 Starting FletConfetti Size Fix Verification...")
    print("Testing simplified Size parsing approach")
    print("Particles should now visibly change size!")
    print("-" * 60)
    ft.app(main)
