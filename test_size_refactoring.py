"""
FletConfetti Size Refactoring Test

This script tests the new Size-based API that replaces separate width/height
properties with Flet's built-in Size dataclass for better consistency.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import flet as ft
from flet.core.size import Size
from flet_confetti import (
    FletConfetti, 
    ConfettiTheme, 
    BlastDirectionality, 
    ParticleShape
)
import math


def main(page: ft.Page):
    page.title = "FletConfetti - Size Refactoring Test"
    page.bgcolor = ft.Colors.BLACK
    page.padding = 20
    page.scroll = ft.ScrollMode.AUTO
    
    # Test the new Size-based API
    confetti_size_demo = FletConfetti(
        theme=ConfettiTheme.NEON,
        particle_shape=ParticleShape.STAR,
        blast_directionality=BlastDirectionality.EXPLOSIVE,
        number_of_particles=20,
        max_blast_force=40,
        gravity=0.3,
        # NEW: Using Size objects instead of separate width/height
        minimum_size=Size(15, 8),      # Instead of minimum_size_width=15, minimum_size_height=8
        maximum_size=Size(35, 20),     # Instead of maximum_size_width=35, maximum_size_height=20
        canvas_size=Size(800, 600),    # Instead of canvas_width=800, canvas_height=600
        duration_seconds=4,
    )
    
    # Test with different size configurations
    confetti_small = FletConfetti(
        colors=["#FF6B6B", "#4ECDC4", "#45B7D1"],
        particle_shape=ParticleShape.HEART,
        blast_directionality=BlastDirectionality.DIRECTIONAL,
        blast_direction=math.pi / 2,
        minimum_size=Size(5, 3),       # Very small particles
        maximum_size=Size(15, 10),
        canvas_size=Size(400, 300),    # Smaller canvas
        duration_seconds=3,
    )
    
    # Test with auto-sizing (None canvas_size)
    confetti_auto = FletConfetti(
        theme=ConfettiTheme.RAINBOW,
        particle_shape=ParticleShape.CIRCLE,
        minimum_size=Size(20, 20),     # Square particles
        maximum_size=Size(40, 40),
        canvas_size=None,              # Auto-size to container
        duration_seconds=3,
    )
    
    status_text = ft.Text(
        "🎯 Size Refactoring Test - New Size-based API",
        size=16,
        color=ft.Colors.WHITE,
        text_align=ft.TextAlign.CENTER,
        weight=ft.FontWeight.BOLD,
    )
    
    def test_size_api(_):
        """Test the new Size-based API."""
        print("🎯 TESTING NEW SIZE-BASED API")
        print("=" * 50)
        
        # Test Size object creation and access
        print(f"✅ minimum_size: {confetti_size_demo.minimum_size}")
        print(f"✅ maximum_size: {confetti_size_demo.maximum_size}")
        print(f"✅ canvas_size: {confetti_size_demo.canvas_size}")
        
        # Test Size object properties
        if confetti_size_demo.minimum_size:
            print(f"   minimum_size.width: {confetti_size_demo.minimum_size.width}")
            print(f"   minimum_size.height: {confetti_size_demo.minimum_size.height}")
        
        # Test serialization to JSON (what gets sent to Dart)
        print(f"✅ minimum_size JSON: {confetti_size_demo._get_attr('minimum_size')}")
        print(f"✅ maximum_size JSON: {confetti_size_demo._get_attr('maximum_size')}")
        print(f"✅ canvas_size JSON: {confetti_size_demo._get_attr('canvas_size')}")
        
        confetti_size_demo.play()
        status_text.value = "✅ Size API test successful!"
        page.update()
    
    def test_small_particles(_):
        """Test small particle configuration."""
        print("🔬 TESTING SMALL PARTICLES")
        print(f"Small particles - min: {confetti_small.minimum_size}, max: {confetti_small.maximum_size}")
        
        confetti_small.play()
        status_text.value = "🔬 Small particles test!"
        page.update()
    
    def test_auto_sizing(_):
        """Test auto-sizing canvas."""
        print("📐 TESTING AUTO-SIZING CANVAS")
        print(f"Auto-sizing canvas: {confetti_auto.canvas_size}")
        
        confetti_auto.play()
        status_text.value = "📐 Auto-sizing test!"
        page.update()
    
    def test_dynamic_size_change(_):
        """Test changing sizes dynamically."""
        print("🔄 TESTING DYNAMIC SIZE CHANGES")
        
        # Change sizes dynamically
        confetti_size_demo.minimum_size = Size(10, 5)
        confetti_size_demo.maximum_size = Size(50, 30)
        confetti_size_demo.canvas_size = Size(1000, 700)
        
        print(f"New minimum_size: {confetti_size_demo.minimum_size}")
        print(f"New maximum_size: {confetti_size_demo.maximum_size}")
        print(f"New canvas_size: {confetti_size_demo.canvas_size}")
        
        confetti_size_demo.play()
        status_text.value = "🔄 Dynamic size change test!"
        page.update()
    
    def test_api_comparison(_):
        """Show API comparison between old and new."""
        print("📊 API COMPARISON")
        print("=" * 50)
        print("OLD API (deprecated):")
        print("  confetti.minimum_size_width = 15")
        print("  confetti.minimum_size_height = 8")
        print("  confetti.maximum_size_width = 35")
        print("  confetti.maximum_size_height = 20")
        print("  confetti.canvas_width = 800")
        print("  confetti.canvas_height = 600")
        print()
        print("NEW API (Size-based):")
        print("  from flet.core.size import Size")
        print("  confetti.minimum_size = Size(15, 8)")
        print("  confetti.maximum_size = Size(35, 20)")
        print("  confetti.canvas_size = Size(800, 600)")
        print()
        print("✅ Benefits:")
        print("  - Consistent with Flet's architecture")
        print("  - Fewer parameters to manage")
        print("  - Type safety with Size dataclass")
        print("  - Cleaner API surface")
        
        status_text.value = "📊 API comparison shown in console!"
        page.update()
    
    def stop_all(_):
        """Stop all confetti animations."""
        confetti_size_demo.stop(clear_all_particles=True)
        confetti_small.stop(clear_all_particles=True)
        confetti_auto.stop(clear_all_particles=True)
        status_text.value = "🛑 All confetti stopped!"
        page.update()
    
    # Create the UI
    page.add(
        ft.Stack(
            expand=True,
            controls=[
                ft.Column(
                    [
                        ft.Text(
                            "🎯 FletConfetti Size Refactoring",
                            size=28,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER,
                        ),
                        
                        ft.Text(
                            "Testing new Size-based API with Flet's Size dataclass",
                            size=16,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                        
                        status_text,
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=40),
                        
                        # Test buttons
                        ft.Row([
                            ft.ElevatedButton(
                                "🎯 Test Size API",
                                on_click=test_size_api,
                                bgcolor=ft.Colors.PURPLE_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🔬 Small Particles",
                                on_click=test_small_particles,
                                bgcolor=ft.Colors.BLUE_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        
                        ft.Row([
                            ft.ElevatedButton(
                                "📐 Auto-sizing",
                                on_click=test_auto_sizing,
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                            ),
                            ft.ElevatedButton(
                                "🔄 Dynamic Change",
                                on_click=test_dynamic_size_change,
                                bgcolor=ft.Colors.ORANGE_600,
                                color=ft.Colors.WHITE,
                            ),
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        
                        ft.ElevatedButton(
                            "📊 API Comparison",
                            on_click=test_api_comparison,
                            bgcolor=ft.Colors.INDIGO_600,
                            color=ft.Colors.WHITE,
                            style=ft.ButtonStyle(
                                padding=ft.Padding(20, 15, 20, 15),
                            ),
                        ),
                        
                        ft.ElevatedButton(
                            "🛑 Stop All",
                            on_click=stop_all,
                            bgcolor=ft.Colors.RED_600,
                            color=ft.Colors.WHITE,
                            style=ft.ButtonStyle(
                                padding=ft.Padding(20, 15, 20, 15),
                            ),
                        ),
                        
                        ft.Divider(color=ft.Colors.WHITE24, height=20),
                        
                        ft.Text(
                            "✨ Refactored: Size-based API for better consistency",
                            size=14,
                            color=ft.Colors.WHITE70,
                            text_align=ft.TextAlign.CENTER,
                            weight=ft.FontWeight.BOLD,
                        ),
                        
                        ft.Text(
                            "Uses Flet's Size dataclass • Cleaner API • Type safety",
                            size=12,
                            color=ft.Colors.WHITE60,
                            text_align=ft.TextAlign.CENTER,
                            italic=True,
                        ),
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=15,
                ),
                
                # Confetti widgets
                confetti_size_demo,
                confetti_small,
                confetti_auto,
            ],
            alignment=ft.alignment.center,
        )
    )


if __name__ == "__main__":
    print("🎯 Starting FletConfetti Size Refactoring Test...")
    print("📏 Testing new Size-based API with Flet's Size dataclass")
    print("🔄 Replacing separate width/height with unified Size objects")
    print("-" * 60)
    ft.app(main)
